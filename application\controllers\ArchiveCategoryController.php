<?php

use PhpOffice\PhpSpreadsheet\Calculation\MathTrig\Arabic;
use PhpOffice\PhpSpreadsheet\Writer\Ods\Thumbnails;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsCategoryArchive $mscategoryarchive
 * @property MsArchive $msarchive
 * @property CI_DB_query_builder $db
 * @property Datatables $datatables
 */
class ArchiveCategoryController extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->model('MsCategoryArchive', 'mscategoryarchive');
        $this->load->model('MsArchive', 'msarchive');
    }

    public function index()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect('auth/login/admin');
        }

        $data = array();
        $data['title'] = 'Arsip Sekolah - Kategori Arsip';
        $data['content'] = 'admin/master/categoryarchive/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponse();
        }

        $datatable =  $this->datatables->make('MsCategoryArchive', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.createdby'] = getCurrentIdUser();

        $data = array();

        foreach ($datatable->getdata($where) as $key => $value) {
            $detail = array();
            $detail[] = $value->name;
            $actions = "<div class=\"d-flex\">  
                <a href=\"" . base_url('master/categoryarchive/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"deleteArchive('" . $value->id .  "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                    <i class=\"ti ti-trash\"></i>
                </button>
            </div>";

            $detail[] = $actions;
            $data[] = $detail;
        }
        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect('auth/login/admin');
        }

        $data = array();
        $data['title'] = 'Arsip Sekolah - Tambah Kategori Arsip';
        $data['content'] = 'admin/master/categoryarchive/add';

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $name = trim(getPost('name'));

        if (empty($name)) {
            return JSONResponseDefault('FAILED', 'Nama kategori arsip tidak boleh kosong');
        }

        $cekArsip = $this->mscategoryarchive->get(array(
            'UPPER(name)' => strtoupper($name),
            'createdby' => getCurrentIdUser()
        ))->num_rows();

        if ($cekArsip > 0) {
            return JSONResponseDefault('FAILED', 'Nama kategori arsip sudah ada');
        }

        $insert = array();
        $insert['name'] = $name;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $insert = $this->mscategoryarchive->insert($insert);

        if ($insert) {
            return JSONResponseDefault('OK', 'Kategori Arsip berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Kategori Arsip gagal ditambahkan');
        }
    }

    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect('auth/login/admin');
        }

        $arsip = $this->mscategoryarchive->get(array('id' => $id));

        if ($arsip->num_rows() == 0) {
            return redirect('master/categoryarchive');
        }

        $data = array();
        $data['title'] = 'Arsip Sekolah - Ubah Kategori Arsip';
        $data['content'] = 'admin/master/categoryarchive/edit';
        $data['category'] = $arsip->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $arsip = $this->mscategoryarchive->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($arsip->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $name = trim(getPost('name'));

        if (empty($name)) {
            return JSONResponseDefault('FAILED', 'Nama kategori arsip tidak boleh kosong');
        }

        $query = $this->mscategoryarchive->get(array(
            'UPPER(name)' => strtoupper($name),
            'id !=' => $id
        ));

        if ($query->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Nama Kategori arsip sudah digunakan');
        }

        $update = array();
        $update['name'] = $name;
        $update['updateddate'] = getCurrentDate();
        $update['updatedby'] = getCurrentIdUser();

        $update = $this->mscategoryarchive->update(array('id' => $id), $update);

        if ($update) {
            return JSONResponseDefault('OK', 'Data kategori arsip berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Data kategori arsip gagal diubah');
        }
    }

    public function process_delete()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = getPost('id');

            if (empty($id)) {
                throw new Exception('Data tidak ditemukan');
            }

            $getarsip = $this->mscategoryarchive->get(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($getarsip->num_rows() == 0) {
                throw new Exception('Data Kategori arsip tidak ditemukan');
            }

            $arsip = $this->msarchive->get(array(
                'categoryid' => $id
            ))->result();

            foreach ($arsip as $item) {
                $filePath = './uploads/archive/' . $item->document;
                if (file_exists($filePath) && !empty($item->document)) {
                    unlink($filePath);
                }
            }

            $this->mscategoryarchive->delete(array('id' => $id));

            $this->msarchive->delete(array('categoryid' => $id));

            if ($this->db->trans_status() === FALSE) {
                $this->db->trans_rollback();
                throw new Exception('Data Kategori Arsip gagal dihapus');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data Kategori Arsip dan arsip terkait berhasil dihapus');
            }
        } catch (Exception $e) {
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }
}
