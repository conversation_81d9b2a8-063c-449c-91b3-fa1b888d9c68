<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<h4 class="py-3 mb-4">
    <span class="text-muted fw-light">CBT / Soal /</span> Detail Soal
</h4>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex align-items-center justify-content-between">
                    <h5>Tambah Soal</h5>
                </div>
            </div>


            <div class="card-body">
                <form id="frmAddQuestion" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <div class="row">
                        <div class="col-md-12">
                            <label for="question" class="form-label">Soal <span class="text-danger">*</span></label>
                            <div id="question"></div>
                        </div>
                        <div class="col-md-6 mt-2">
                            <div class="form-check form-check-inline mt-3 mb-3">
                                <input type="radio" class="form-check-input" name="correct" id="correct-A" value="1">
                                <label for="correct-A" class="form-check-label">Jawaban (Pilih jika ini jawaban benar)</label>
                            </div>
                            <div id="answer-A"></div>
                        </div>
                        <div class="col-md-6 mt-2">
                            <div class="form-check form-check-inline mt-3 mb-3">
                                <input type="radio" class="form-check-input" name="correct" id="correct-B" value="2">
                                <label for="correct-B" class="form-check-label">Jawaban (Pilih jika ini jawaban benar)</label>
                            </div>
                            <div id="answer-B"></div>
                        </div>
                        <div class="col-md-6 mt-2">
                            <div class="form-check form-check-inline mt-3 mb-3">
                                <input type="radio" class="form-check-input" name="correct" id="correct-C" value="3">
                                <label for="correct-C" class="form-check-label">Jawaban (Pilih jika ini jawaban benar)</label>
                            </div>
                            <div id="answer-C"></div>
                        </div>
                        <div class="col-md-6 mt-2">
                            <div class="form-check form-check-inline mt-3 mb-3">
                                <input type="radio" class="form-check-input" name="correct" id="correct-D" value="4">
                                <label for="correct-D" class="form-check-label">Jawaban (Pilih jika ini jawaban benar)</label>
                            </div>
                            <div id="answer-D"></div>
                        </div>
                        <div class="d-flex justify-content-end mt-4">
                            <a href="<?= base_url('cbt/question') ?>" class="btn btn-danger">
                                <i class="ti ti-arrow-back me-2"></i>
                                Kembali
                            </a>

                            <button type="submit" class="btn btn-primary ms-2">
                                <i class="ti ti-check me-2"></i>
                                Simpan
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-2">
            <div class="card-header">
                <div class="d-flex align-items-center justify-content-between">
                    <h5>Soal</h5>
                </div>
            </div>

            <div class="card-body">
                <div class="row">
                    <div class="col-md-12" id="tablequestion">

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    window.onload = function() {
        getQuestion();
        let questioneditor = new Quill("#question", {
            bounds: "#question",
            placeholder: "Type Something...",
            modules: {
                formula: !0,
                toolbar: [
                    [{
                        font: []
                    }, {
                        size: []
                    }],
                    ["bold", "italic", "underline", "strike"],
                    [{
                        color: []
                    }, {
                        background: []
                    }],
                    [{
                        script: "super"
                    }, {
                        script: "sub"
                    }],
                    [{
                        header: "1"
                    }, {
                        header: "2"
                    }, "blockquote", "code-block"],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }, {
                        indent: "-1"
                    }, {
                        indent: "+1"
                    }],
                    [{
                        direction: "rtl"
                    }],
                    ["link", "image", "video", "formula"],
                    ["clean"]
                ]
            },
            theme: "snow"
        });

        let answerAeditor = new Quill("#answer-A", {
            bounds: "#answer-A",
            placeholder: "Type Something...",
            modules: {
                formula: !0,
                toolbar: [
                    [{
                        font: []
                    }, {
                        size: []
                    }],
                    ["bold", "italic", "underline", "strike"],
                    [{
                        color: []
                    }, {
                        background: []
                    }],
                    [{
                        script: "super"
                    }, {
                        script: "sub"
                    }],
                    [{
                        header: "1"
                    }, {
                        header: "2"
                    }, "blockquote", "code-block"],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }, {
                        indent: "-1"
                    }, {
                        indent: "+1"
                    }],
                    [{
                        direction: "rtl"
                    }],
                    ["link", "image", "video", "formula"],
                    ["clean"]
                ]
            },
            theme: "snow"
        });

        let answerBeditor = new Quill("#answer-B", {
            bounds: "#answer-B",
            placeholder: "Type Something...",
            modules: {
                formula: !0,
                toolbar: [
                    [{
                        font: []
                    }, {
                        size: []
                    }],
                    ["bold", "italic", "underline", "strike"],
                    [{
                        color: []
                    }, {
                        background: []
                    }],
                    [{
                        script: "super"
                    }, {
                        script: "sub"
                    }],
                    [{
                        header: "1"
                    }, {
                        header: "2"
                    }, "blockquote", "code-block"],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }, {
                        indent: "-1"
                    }, {
                        indent: "+1"
                    }],
                    [{
                        direction: "rtl"
                    }],
                    ["link", "image", "video", "formula"],
                    ["clean"]
                ]
            },
            theme: "snow"
        });

        let answerCeditor = new Quill("#answer-C", {
            bounds: "#answer-C",
            placeholder: "Type Something...",
            modules: {
                formula: !0,
                toolbar: [
                    [{
                        font: []
                    }, {
                        size: []
                    }],
                    ["bold", "italic", "underline", "strike"],
                    [{
                        color: []
                    }, {
                        background: []
                    }],
                    [{
                        script: "super"
                    }, {
                        script: "sub"
                    }],
                    [{
                        header: "1"
                    }, {
                        header: "2"
                    }, "blockquote", "code-block"],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }, {
                        indent: "-1"
                    }, {
                        indent: "+1"
                    }],
                    [{
                        direction: "rtl"
                    }],
                    ["link", "image", "video", "formula"],
                    ["clean"]
                ]
            },
            theme: "snow"
        });

        let answerDeditor = new Quill("#answer-D", {
            bounds: "#answer-D",
            placeholder: "Type Something...",
            modules: {
                formula: !0,
                toolbar: [
                    [{
                        font: []
                    }, {
                        size: []
                    }],
                    ["bold", "italic", "underline", "strike"],
                    [{
                        color: []
                    }, {
                        background: []
                    }],
                    [{
                        script: "super"
                    }, {
                        script: "sub"
                    }],
                    [{
                        header: "1"
                    }, {
                        header: "2"
                    }, "blockquote", "code-block"],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }, {
                        indent: "-1"
                    }, {
                        indent: "+1"
                    }],
                    [{
                        direction: "rtl"
                    }],
                    ["link", "image", "video", "formula"],
                    ["clean"]
                ]
            },
            theme: "snow"
        });

        questioneditor.getModule('toolbar').addHandler('image', function() {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.onchange = function() {
                var file = input.files[0];
                var reader = new FileReader();
                reader.onload = function() {
                    var img = new Image();
                    img.src = reader.result;
                    img.onload = function() {
                        // Periksa ukuran gambar
                        if (img.width > 500 || img.height > 500) {
                            return swalMessageFailed('Ukuran gambar terlalu besar. Harap unggah gambar dengan ukuran maksimum 500x500 piksel.');
                        } else {
                            var range = questioneditor.getSelection(true);
                            questioneditor.insertEmbed(range.index, 'image', img.src);
                        }
                    };
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });

        answerAeditor.getModule('toolbar').addHandler('image', function() {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.onchange = function() {
                var file = input.files[0];
                var reader = new FileReader();
                reader.onload = function() {
                    var img = new Image();
                    img.src = reader.result;
                    img.onload = function() {
                        // Periksa ukuran gambar
                        if (img.width > 500 || img.height > 500) {
                            return swalMessageFailed('Ukuran gambar terlalu besar. Harap unggah gambar dengan ukuran maksimum 500x500 piksel.');
                        } else {
                            var range = answerAeditor.getSelection(true);
                            answerAeditor.insertEmbed(range.index, 'image', img.src);
                        }
                    };
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });

        answerBeditor.getModule('toolbar').addHandler('image', function() {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.onchange = function() {
                var file = input.files[0];
                var reader = new FileReader();
                reader.onload = function() {
                    var img = new Image();
                    img.src = reader.result;
                    img.onload = function() {
                        // Periksa ukuran gambar
                        if (img.width > 500 || img.height > 500) {
                            return swalMessageFailed('Ukuran gambar terlalu besar. Harap unggah gambar dengan ukuran maksimum 500x500 piksel.');
                        } else {
                            var range = answerBeditor.getSelection(true);
                            answerBeditor.insertEmbed(range.index, 'image', img.src);
                        }
                    };
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });

        answerCeditor.getModule('toolbar').addHandler('image', function() {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.onchange = function() {
                var file = input.files[0];
                var reader = new FileReader();
                reader.onload = function() {
                    var img = new Image();
                    img.src = reader.result;
                    img.onload = function() {
                        // Periksa ukuran gambar
                        if (img.width > 500 || img.height > 500) {
                            return swalMessageFailed('Ukuran gambar terlalu besar. Harap unggah gambar dengan ukuran maksimum 500x500 piksel.');
                        } else {
                            var range = answerCeditor.getSelection(true);
                            answerCeditor.insertEmbed(range.index, 'image', img.src);
                        }
                    };
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });

        answerDeditor.getModule('toolbar').addHandler('image', function() {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.onchange = function() {
                var file = input.files[0];
                var reader = new FileReader();
                reader.onload = function() {
                    var img = new Image();
                    img.src = reader.result;
                    img.onload = function() {
                        // Periksa ukuran gambar
                        if (img.width > 500 || img.height > 500) {
                            return swalMessageFailed('Ukuran gambar terlalu besar. Harap unggah gambar dengan ukuran maksimum 500x500 piksel.');
                        } else {
                            var range = answerDeditor.getSelection(true);
                            answerDeditor.insertEmbed(range.index, 'image', img.src);
                        }
                    };
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });

        $('#frmAddQuestion').submit(function(e) {
            e.preventDefault();
            $('button[type="submit"]').attr('disabled');
            let question = cleanQuillContent($('#question .ql-editor').html());
            let answerA = cleanQuillContent($('#answer-A .ql-editor').html());
            let answerB = cleanQuillContent($('#answer-B .ql-editor').html());
            let answerC = cleanQuillContent($('#answer-C .ql-editor').html());
            let answerD = cleanQuillContent($('#answer-D .ql-editor').html());
            let correct = $('input[name="correct"]:checked').val();

            // Validasi pertanyaan tidak boleh kosong atau hanya spasi
            if (!question || question.trim() === '' || question === '<p><br></p>' || question === '<p></p>') {
                swalMessageFailed('Pertanyaan tidak boleh kosong');
                $('button[type="submit"]').removeAttr('disabled');
                return;
            }

            let answer = [];

            if (answerA != '') {
                answer.push({
                    answer: answerA,
                    coulum: 1
                });
            }

            if (answerB != '') {
                answer.push({
                    answer: answerB,
                    coulum: 2
                });
            }

            if (answerC != '') {
                answer.push({
                    answer: answerC,
                    coulum: 3
                });
            }

            if (answerD != '') {
                answer.push({
                    answer: answerD,
                    coulum: 4
                });
            }

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                dataType: 'JSON',
                data: {
                    question: question,
                    answer: answer,
                    correct: correct
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {

                        return swalMessageSuccess(response.MESSAGE, (ok) => {
                            getQuestion();
                            $('#button[type="submit"]').removeAttr('disabled');
                        });
                    } else {
                        $('#button[type="submit"]').removeAttr('disabled');
                        return swalMessageFailed(response.MESSAGE);
                    }
                },
                error: function() {
                    $('#button[type="submit"]').removeAttr('disabled');
                    return swalError();
                }
            });
        });
    };

    function cleanQuillContent(content) {
        content = content.trim();

        const strippedContent = content.replace(/(<([^>]+)>)/gi, "").trim();

        return strippedContent.length === 0 ? "" : content;
    }

    function getQuestion() {

        $('#question .ql-editor').empty();
        $('#answer-A .ql-editor').empty();
        $('#answer-B .ql-editor').empty();
        $('#answer-C .ql-editor').empty();
        $('#answer-D .ql-editor').empty();
        $('input[name="correct"]').prop('checked', false);

        $.ajax({
            url: '<?= base_url(uri_string() . '/getquestion') ?>',
            type: 'POST',
            dataType: 'HTML',
            success: function(response) {
                $('#tablequestion').html(response);
            }
        });
    }

    function editData(id) {
        $.ajax({
            url: '<?= base_url(uri_string() . '/edit') ?>',
            type: 'POST',
            dataType: 'JSON',
            data: {
                id: id
            },
            success: function(response) {
                $('#ModalGlobal').html(response.CONTENT);
                $('#ModalGlobal').modal('show');
            }
        });
    }
</script>