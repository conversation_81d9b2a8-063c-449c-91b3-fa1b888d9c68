<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsUsers $msusers
 */
class PrincipalController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsPrincipal', 'msprincipal');
    }

    public function index()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect('auth/login');
        }

        $data = array();
        $data['title'] = 'Profil Sekolah - Kepala Sekolah';
        $data['content'] = 'admin/settings/principal/index';
        $data['settings'] = $this->msusers->get(array('id' => getCurrentIdUser()))->row();
        $data['introduction'] = $this->msprincipal->get(array('user_id' => getCurrentIdUser()))->row();

        return $this->load->view('master', $data);
    }

    public function process()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses!');
        }

        $userId = getCurrentIdUser();

        // Cek apakah data user valid
        $cekUser = $this->msusers->get(array('id' => $userId));
        if ($cekUser->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data pengguna tidak ditemukan');
        }

        $cekPrincipal = $this->msprincipal->get(array('user_id' => $userId));

        $principal = getPost('principal');
        $introduction = getPost('introduction');
        $picture = $_FILES['profile'];

        // Validasi input
        if (empty(trim($principal))) {
            return JSONResponseDefault('FAILED', 'Nama kepala sekolah tidak boleh kosong');
        }

        // Validasi kata pengantar - hapus HTML tags dan cek apakah hanya berisi spasi
        $cleanIntroduction = trim(strip_tags($introduction));
        if (empty($cleanIntroduction) || $introduction === '<p><br></p>' || $introduction === '<p></p>') {
            return JSONResponseDefault('FAILED', 'Kata Pengantar tidak boleh kosong');
        }

        $updateData = array(
            'introduction' => $introduction
        );

        // Proses upload gambar profil
        if ($picture['size'] > 0) {
            $config = array(
                'allowed_types' => 'jpg|jpeg|png',
                'upload_path' => './uploads/principal',
                'encrypt_name' => true,
            );

            $this->load->library('upload', $config);

            if ($this->upload->do_upload('profile')) {
                $uploadData = $this->upload->data();
                $updateData['picture'] = $uploadData['file_name'];


                if ($cekPrincipal->num_rows() > 0) {
                    $rowPrincipal = $cekPrincipal->row();
                    if (!empty($rowPrincipal->picture) && file_exists('./uploads/principal/' . $rowPrincipal->picture)) {
                        @unlink('./uploads/principal/' . $rowPrincipal->picture);
                    }
                }
            } else {
                return JSONResponseDefault('FAILED', 'Gagal mengunggah foto profil: ' . strip_tags($this->upload->display_errors()));
            }
        }

        if ($cekPrincipal->num_rows() > 0) {
            $updateData['updateddate'] = getCurrentDate();
            $updateData['updatedby'] = $userId;

            $updated = $this->msprincipal->update(array('user_id' => $userId), $updateData);
            if (!$updated) {
                return JSONResponseDefault('FAILED', 'Data gagal diperbarui');
            }
        } else {
            $updateData['user_id'] = $userId;
            $updateData['createddate'] = getCurrentDate();
            $updateData['createdby'] = $userId;

            $inserted = $this->msprincipal->insert($updateData);
            if (!$inserted) {
                return JSONResponseDefault('FAILED', 'Data gagal ditambahkan');
            }
        }

        //Update nama kepala sekolah di tabel msusers
        $updateMsUsers = array(
            'principal' => strtoupper($principal), // Format nama menjadi huruf besar
            'updatedby' => $userId,
            'updateddate' => getCurrentDate()
        );
        $userUpdated = $this->msusers->update(array('id' => $userId), $updateMsUsers);
        if (!$userUpdated) {
            return JSONResponseDefault('FAILED', 'Nama kepala sekolah gagal diperbarui');
        }

        return JSONResponseDefault('OK', 'Data berhasil disimpan');
    }
}
