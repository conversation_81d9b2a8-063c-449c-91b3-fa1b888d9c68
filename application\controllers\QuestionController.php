<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsQuestion $msquestion
 * @property MsQuestionmaterial $msquestionmaterial
 * @property MsAnswer $msanswer
 * @property MsCourse $mscourse
 * @property CI_mysqli_driver $db
 * @property Datatables $datatables
 * @property TbCbt $tbcbt
 */
class QuestionController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsQuestion', 'msquestion');
        $this->load->model('MsQuestionmaterial', 'msquestionmaterial');
        $this->load->model('MsAnswer', 'msanswer');
        $this->load->model('MsCourse', 'mscourse');
        $this->load->model('TbCbt', 'tbcbt');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsTeacher', 'msteacher');
    }

    public function index()
    {
        if (!isLogin() || (!isAdmin() && !isTeacher())) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Soal';
        $data['content'] = 'admin/cbt/question/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('MsQuestionmaterial', 'QueryDatatables', 'SearchDatatables');

        $where = array();

        if (isAdmin()) {
            $where['a.createdby'] = getCurrentIdUser();
        } else {
            $where['a.createdby'] = getCurrentIdSchool();
        }

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('cbt/question/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm mb-1 me-1\" data-bs-toggle=\"tooltip\" data-bs-custom-class=\"tooltip-primary\" data-bs-placement=\"top\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                <button type=\"button\" class=\"btn btn-danger btn-sm mb-1 me-1\" onclick=\"deleteQuestion('" . $value->id . "','" . $value->name . "')\" data-bs-toggle=\"tooltip\" data-bs-custom-class=\"tooltip-danger\" data-bs-placement=\"top\" data-bs-original-title=\"Soal\">
                    <i class=\"ti ti-trash\"></i>
                </button>

                <a href=\"" . base_url('cbt/question/detail/' . $value->id) . "\" class=\"btn btn-warning btn-sm mb-1\" data-bs-toggle=\"tooltip\" data-bs-custom-class=\"tooltip-warning\" data-bs-placement=\"top\" data-bs-original-title=\"Soal\">
                    <i class=\"ti ti-notebook\"></i>
                </a>
            </div>";

            $detail[] = $value->name;
            $detail[] = $value->coursename;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || (!isAdmin() && !isTeacher())) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Tambah Soal';
        $data['content'] = 'admin/cbt/question/add';
        $data['course'] = $this->mscourse->order_by('name', 'ASC')->get(array('createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()))->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || (!isAdmin() && !isTeacher())) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $name = getPost('name');
            $courseid = getPost('courseid');

            if (empty(strlen(trim($name)))) {
                throw new Exception('Nama materi soal tidak boleh kosong');
            } elseif ($courseid == null) {
                throw new Exception('Mata Pelajaran tidak boleh kosong');
            }

            $insert = array();
            $insert['name'] = strtoupper($name);
            $insert['courseid'] = $courseid;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = isAdmin() ? getCurrentIdUser() : getCurrentIdSchool();

            $this->msquestionmaterial->insert($insert);
            $lastid = $this->db->insert_id();

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal ditambahkan');
            } else {
                $this->db->trans_commit();
                return JSONResponse(array(
                    'RESULT' => 'OK',
                    'MESSAGE' => 'Data berhasil ditambahkan',
                    'REDIRECT' => base_url('cbt/question/detail/' . $lastid)
                ));
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin() || (!isAdmin() && !isTeacher())) {
            redirect(base_url('auth/login/admin'));
        }

        $cek = $this->msquestionmaterial->get(array(
            'id' => $id
        ));

        if ($cek->num_rows() == 0) {
            return redirect(base_url('cbt/question'));
        }

        $data = array();
        $data['title'] = 'Ubah Soal';
        $data['content'] = 'admin/cbt/question/edit';
        $data['course'] = $this->mscourse->order_by('name', 'ASC')->get(array(
            'createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()
        ))->result();
        $data['questionmaterial'] = $cek->row();

        return $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || (!isAdmin() && !isTeacher())) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $cek = $this->msquestionmaterial->get(array(
                'id' => $id
            ));

            if ($cek->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $name = getPost('name');
            $courseid = getPost('courseid');

            if (empty(strlen(trim($name)))) {
                throw new Exception('Nama materi soal tidak boleh kosong');
            } elseif ($courseid == null) {
                throw new Exception('Mata Pelajaran tidak boleh kosong');
            }

            $update = array();
            $update['name'] = strtoupper($name);
            $update['courseid'] = $courseid;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = isAdmin() ? getCurrentIdUser() : getCurrentIdSchool();

            $update = $this->msquestionmaterial->update(array('id' => $id), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal diubah');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil diubah');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_delete()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || (!isAdmin() && !isTeacher())) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $id = getPost('id');

            $get = $this->msquestionmaterial->get(array(
                'id' => $id
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $cbt = $this->tbcbt->total(array(
                'questionmaterialid' => $id
            ));

            if ($cbt > 0) {
                throw new Exception('Data tidak bisa dihapus, karena sudah digunakan pada jadwal ujian');
            }

            $this->msquestionmaterial->delete(array('id' => $id));
            $this->msquestion->delete(array('questionmaterialid' => $id));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal dihapus');
            } else {
                $this->db->trans_commit();

                return JSONResponseDefault('OK', 'Data berhasil dihapus');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function detail($id)
    {
        if (!isLogin() || (!isAdmin() && !isTeacher())) {
            redirect(base_url('auth/login/admin'));
        }

        $cek = $this->msquestionmaterial->get(array(
            'id' => $id
        ));

        if ($cek->num_rows() == 0) {
            return redirect(base_url('cbt/question'));
        }

        $data = array();
        $data['title'] = 'Detail Soal';
        $data['content'] = 'admin/cbt/question/detail';
        $data['question'] = $this->msquestion->get(array('questionmaterialid' => $id))->result();

        return $this->load->view('master', $data);
    }


    public function edit_detail($id)
    {
        if (!isLogin() || (!isAdmin() && !isTeacher())) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $detailid = getPost('id');

        $cek = $this->msquestionmaterial->get(array(
            'id' => $id
        ));

        if ($cek->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $get = $this->msquestion->get(array(
            'id' => $detailid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $getansewer = $this->msanswer->get(array(
            'questionid' => $detailid
        ))->result();

        $data = array();
        for ($i = 0; $i < 4; $i++) {
            if ($i == 0) {
                $data['answerA'] = isset($getansewer[$i]) ? $getansewer[$i]->answer : null;
                $data['checkedA'] = isset($getansewer[$i]) && $getansewer[$i]->is_correct == 1 ? 'checked' : '';
            } else if ($i == 1) {
                $data['answerB'] = isset($getansewer[$i]) ? $getansewer[$i]->answer : null;
                $data['checkedB'] = isset($getansewer[$i]) && $getansewer[$i]->is_correct == 1 ? 'checked' : '';
            } else if ($i == 2) {
                $data['answerC'] = isset($getansewer[$i]) ? $getansewer[$i]->answer : null;
                $data['checkedC'] = isset($getansewer[$i]) && $getansewer[$i]->is_correct == 1 ? 'checked' : '';
            } else if ($i == 3) {
                $data['answerD'] = isset($getansewer[$i]) ? $getansewer[$i]->answer : null;
                $data['checkedD'] = isset($getansewer[$i]) && $getansewer[$i]->is_correct == 1 ? 'checked' : '';
            }
        }
        $data['question'] = $get->row()->question;
        $data['id'] = $detailid;

        return JSONResponse(array(
            'CONTENT' => $this->load->view('admin/cbt/question/editdetail', $data, true)
        ));
    }

    public function process_edit_detail($id)
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || (!isAdmin() && !isTeacher())) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $question = getPost('question', '');
            $answer = getPost('answer', array());
            $correct = getPost('correct');
            $detailid = getPost('id');

            if (empty(strlen(trim($question)))) {
                throw new Exception('Pertanyaan tidak boleh kosong');
            } elseif (count($answer) < 2) {
                throw new Exception('Pilihan jawaban tidak boleh kurang dari 2');
            } elseif (empty(strlen(trim($correct)))) {
                throw new Exception('Jawaban benar tidak boleh kosong');
            } else if (!in_array($correct, array_column($answer, 'coulum'))) {
                throw new Exception('Jawaban benar tidak ditemukan dalam pilihan jawaban');
            }

            $update = array();
            $update['question'] = $question;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msquestion->update(array('id' => $detailid), $update);

            $this->msanswer->delete(array('questionid' => $detailid));

            foreach ($answer as $key => $value) {
                $insertanswer = array();
                $insertanswer['questionid'] = $detailid;
                $insertanswer['answer'] = $value['answer'];
                $insertanswer['is_correct'] = $correct == $value['coulum'] ? 1 : 0;
                $insertanswer['createddate'] = getCurrentDate();
                $insertanswer['createdby'] = getCurrentIdUser();

                $this->msanswer->insert($insertanswer);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal diubah');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil diubah');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_detail($id)
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || (!isAdmin() && !isTeacher())) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $cek = $this->msquestionmaterial->get(array(
                'id' => $id
            ));

            if ($cek->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $question = getPost('question', '');
            $answer = getPost('answer', array());
            $correct = getPost('correct', '');

            if (empty(strlen(trim($question)))) {
                throw new Exception('Pertanyaan tidak boleh kosong');
            } elseif (count($answer) < 2) {
                throw new Exception('Pilihan jawaban tidak boleh kurang dari 2');
            } elseif (empty(strlen(trim($correct)))) {
                throw new Exception('Jawaban benar tidak boleh kosong');
            } else if (!in_array($correct, array_column($answer, 'coulum'))) {
                throw new Exception('Jawaban benar tidak ditemukan dalam pilihan jawaban');
            }

            $insert = array();
            $insert['question'] = $question;
            $insert['questionmaterialid'] = $id;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = isAdmin() ? getCurrentIdUser() : getCurrentIdSchool();

            $this->msquestion->insert($insert);
            $lastid = $this->db->insert_id();

            foreach ($answer as $key => $value) {
                $insertanswer = array();
                $insertanswer['questionid'] = $lastid;
                $insertanswer['answer'] = $value['answer'];
                $insertanswer['is_correct'] = $correct == $value['coulum'] ? 1 : 0;
                $insertanswer['createddate'] = getCurrentDate();
                $insertanswer['createdby'] = isAdmin() ? getCurrentIdUser() : getCurrentIdSchool();

                $this->msanswer->insert($insertanswer);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal ditambahkan');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function process_delete_detail()
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || (!isAdmin() && !isTeacher())) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $id = getPost('id');
            $password = getPost('password');

            // Validasi password
            if (empty($password)) {
                throw new Exception('Password tidak boleh kosong');
            }

            // Verifikasi password berdasarkan role user
            if (isAdmin()) {
                $user = $this->msusers->get(array('id' => getCurrentIdUser()));
                if ($user->num_rows() == 0 || !password_verify($password, $user->row()->password)) {
                    throw new Exception('Password yang anda masukkan salah');
                }
            } elseif (isTeacher()) {
                $teacher = $this->msteacher->get(array('id' => getCurrentIdSchool()));
                if ($teacher->num_rows() == 0 || !password_verify($password, $teacher->row()->password)) {
                    throw new Exception('Password yang anda masukkan salah');
                }
            }

            $get = $this->msquestion->get(array(
                'id' => $id
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $this->msquestion->delete(array('id' => $id));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal dihapus');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil dihapus');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function getQuestion($id)
    {
        if (!isLogin() || (!isAdmin() && !isTeacher())) {
            return;
        } else {
            $data = array();
            $data['question'] = $this->msquestion->select('a.*')
                ->join('msanswer b', 'a.id = b.questionid')
                ->where(array('a.questionmaterialid' => $id))
                ->group_by('a.id')
                ->get();

            echo $this->load->view('admin/cbt/question/getquestion', $data, true);
        }
    }
}
