<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsClass $msclass
 * @property MsTeacher $msteacher
 * @property MsCourse $mscourse
 * @property MsCoursedetail $mscoursedetail
 * @property MsScheduleclass $msscheduleclass
 * @property MsHomework $mshomework
 * @property MsHomeworkdetail $mshomeworkdetail
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 * @property Datatables $datatables
 */
class HomeworkController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsClass', 'msclass');
        $this->load->model('MsTeacher', 'msteacher');
        $this->load->model('MsCourse', 'mscourse');
        $this->load->model('MsCoursedetail', 'mscoursedetail');
        $this->load->model('MsScheduleclass', 'msscheduleclass');
        $this->load->model('MsHomework', 'mshomework');
        $this->load->model('MsHomeworkdetail', 'mshomeworkdetail');
    }

    public function index()
    {
        if (!isLogin() || !isTeacher()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Manajemen Tugas Peserta Didik';
        $data['content'] = 'admin/master/homework/index';

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isTeacher()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('MsScheduleclass', 'QueryDatatables_homework', 'SearchDatatables_homework');

        $where = array();
        $where['d.teacherid'] = getCurrentIdUser();
        $where['a.academicyeardetailid'] = getSessionValue('ACADEMICYEARDETAILID');

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();

            $actions = "<div class=\"d-flex\">
                    <a href=\"" . base_url('master/homework/detail/' . stringEncryption('encrypt', json_encode(array('classid' => $value->classid, 'courseid' => $value->courseid)))) . "\" class=\"btn btn-primary btn-sm mr-1\">
                        <i class=\"ti ti-eye\"></i>
                    </a>
                </div>";

            $detail[] = $value->coursename;
            $detail[] = ($value->level ?? null) == null ? $value->classname : $value->level . ' ' . $value->classname;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function detail($params)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $params = json_decode(stringEncryption('decrypt', $params), true);

        $classid = $params['classid'] ?? null;
        $courseid = $params['courseid'] ?? null;

        $get = $this->msscheduleclass->get(array(
            'classid' => $classid,
            'courseid' => $courseid
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('master/schedule/class'));
        }

        $days = array();

        foreach ($get->result() as $row) {
            $days[] = $row->days;
        }

        $days = implode(',', $days);

        $row = $get->row();

        $data = array();
        $data['title'] = 'Manajemen - Tugas Peserta Didik';
        $data['content'] = 'admin/master/homework/detail';
        $data['class'] = $this->msclass->select('id,name,level')
            ->where(array('createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()))
            ->order_by('name', 'ASC')
            ->get()
            ->result();
        $data['course'] = $this->mscourse->select('id,name')
            ->where(array('createdby' => isAdmin() ? getCurrentIdUser() : getCurrentIdSchool()))
            ->order_by('a.name', 'ASC')
            ->get()->result();
        $data['teacher'] = $this->mscoursedetail->select('b.id,b.name')
            ->join('msteacher b', 'a.teacherid = b.id', 'LEFT')
            ->where('a.courseid', $row->courseid)
            ->order_by('b.name', 'ASC')
            ->group_by('b.id')
            ->get()
            ->result();
        $data['days'] = $days;
        $data['scheduleclass'] = $row;


        return $this->load->view('master', $data);
    }

    public function datatables_homework($params)
    {
        if (!isLogin()) {
            return JSONResponse();
        }

        $params = json_decode(stringEncryption('decrypt', $params), true);

        $classid = $params['classid'] ?? null;
        $courseid = $params['courseid'] ?? null;

        $datatable = $this->datatables->make('MsHomework', 'QueryDatatables', 'SearchDatatables');

        $where = array();
        $where['a.courseid'] = $courseid;
        $where['a.classid'] = $classid;
        $where['a.academicyeardetailid'] = getSessionValue('ACADEMICYEARDETAILID');

        $data = array();
        foreach ($datatable->getData($where) as $key => $value) {
            $detail = array();
            $actions = '';
            if (isTeacher()) {
                $actions .= "<div class=\"d-flex\">
                    <button onclick=\"modalEditHomework('" . $value->id . "')\"  class=\"btn btn-primary btn-sm ms-2 mb-1\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                        <i class=\"ti ti-edit\"></i>
                    </button>

                    <button type=\"button\" class=\"btn btn-danger btn-sm ms-2 mb-1\" onclick=\"deleteHomework('" . $value->id . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                        <i class=\"ti ti-trash\"></i>
                    </button>
                    <a href=\"" . base_url('master/homework/evaluation/' . $value->id) . "\" class=\"btn btn-success btn-sm ms-2 mb-1\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-success\" data-bs-original-title=\"Peserta Didik\">
                        <i class=\"ti ti-users\"></i>
                    </a>
                </div>";
            }

            if ($value->document != null && file_exists('uploads/homework/' . $value->document)) {
                $actions .= "<div class=\"d-flex\">
                    <a href=\"" . base_url('uploads/homework/' . $value->document) . "\" class=\"btn btn-success btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-success\" data-bs-original-title=\"Unduh\" download>
                        <i class=\"ti ti-download\"></i>
                    </a>
                </div>";
            } else if (!isTeacher()) {
                $actions .= "N/A";
            }

            $datenow = time();
            $destination_date = strtotime($value->collectiondate);
            $difference_seconds = $destination_date - $datenow;
            $difference_day = ceil($difference_seconds / (60 * 60 * 24));

            $status = '';
            if (getCurrentDate('Y-m-d') == $value->collectiondate) {
                $status = '<span class="badge bg-primary">Hari ini</span>';
            } else if ($difference_day == 0) {
                $status = '<span class="badge bg-success">Besok</span>';
            } else if ($difference_day >= 1) {
                $status = '<span class="badge bg-warning">' . $difference_day . ' Hari Lagi </span>';
            } else {
                $status = '<span class="badge bg-danger">' . abs($difference_day) . ' Hari yang lalu</span>';
            }

            $detail[] = $value->name;
            $detail[] = tgl_indo($value->collectiondate) . ' ' . $value->collectiontime;
            $detail[] = $status;
            $detail[] = $value->description ?? '-';
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function add_homework($params)
    {
        if (!isLogin() || !isTeacher()) {
            return redirect(base_url('auth/login/admin'));
        }

        $params = json_decode(stringEncryption('decrypt', $params), true);

        $classid = $params['classid'] ?? null;
        $courseid = $params['courseid'] ?? null;

        $get = $this->msscheduleclass->get(array(
            'classid' => $classid,
            'courseid' => $courseid
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $data = array();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('admin/master/homework/add', $data, true)
        ));
    }

    public function process_add_homework($params)
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || !isTeacher()) {
                throw new Exception('Anda tidak memiliki akses untuk menambahkan data');
            }

            $params = json_decode(stringEncryption('decrypt', $params), true);

            $classid = $params['classid'] ?? null;
            $courseid = $params['courseid'] ?? null;

            $get = $this->mscourse->get(array(
                'id' => $courseid
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $name = getPost('name');
            $collectiondate = getPost('collectiondate');
            $collectiontime = getPost('collectiontime');
            $document = $_FILES['document'];
            $description = getPost('description', null);

            if (empty(strlen(trim($name)))) {
                throw new Exception('Nama tugas tidak boleh kosong');
            } else if ($collectiondate == null) {
                throw new Exception('Tanggal akhir pengumpulan tidak boleh kosong');
            } else if ($collectiondate < getCurrentDate('Y-m-d')) {
                throw new Exception('Tanggal akhir pengumpulan tidak boleh kurang dari tanggal sekarang');
            } else if ($collectiontime == null) {
                throw new Exception('Jam akhir pengumpulan tidak boleh kosong');
            }

            // Validasi kombinasi tanggal dan waktu
            $collectionDateTime = $collectiondate . ' ' . $collectiontime;
            $currentDateTime = getCurrentDate('Y-m-d') . ' ' . getCurrentTime('H:i');
            if (strtotime($collectionDateTime) <= strtotime($currentDateTime)) {
                throw new Exception('Waktu akhir pengumpulan harus lebih besar dari waktu sekarang');
            }

            $config = array();
            $config['allowed_types'] = 'jpg|jpeg|png|pdf|doc|docx';
            $config['upload_path'] = './uploads/homework';
            $config['encrypt_name'] = true;

            $this->load->library('upload', $config);

            $insert = array();

            if ($document['size'] > 0) {
                if ($this->upload->do_upload('document')) {
                    $data = $this->upload->data();
                    $insert['document'] = $data['file_name'];
                } else {
                    throw new Exception('Gagal mengunggah dokumen');
                }
            }

            $insert['name'] = $name;
            $insert['teacherid'] = getCurrentIdUser();
            $insert['courseid'] = $courseid;
            $insert['classid'] = $classid;
            $insert['collectiondate'] = $collectiondate;
            $insert['collectiontime'] = $collectiontime;
            $insert['description'] = $description;
            $insert['createdby'] = getCurrentIdSchool();
            $insert['createddate'] = getCurrentDate();
            $insert['academicyeardetailid'] = getSessionValue('ACADEMICYEARDETAILID');

            $this->mshomework->insert($insert);
            $homeworkid = $this->db->insert_id();

            $student = $this->msclass->select('a.*,b.id as studentid')
                ->join('msstudent b', 'b.classid = a.id')
                ->where(array(
                    'a.id' => $classid,
                    'a.isdeleted' => null,
                    'b.status' => 'Active',
                    'b.isdeleted' => null
                ))
                ->get()
                ->result();

            foreach ($student as $row) {
                $insertdetail = array();
                $insertdetail['homeworkid'] = $homeworkid;
                $insertdetail['studentid'] = $row->studentid;
                $insertdetail['status'] = 'Belum Mengerjakan';
                $insertdetail['createdby'] = getCurrentIdSchool();
                $insertdetail['createddate'] = getCurrentDate();

                $this->mshomeworkdetail->insert($insertdetail);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal ditambahkan');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function edit_homework($params)
    {
        if (!isLogin() || !isTeacher()) {
            return redirect(base_url('auth/login/admin'));
        }

        $homeworkid = getPost('homeworkid');

        $get = $this->mshomework->get(array('id' => $homeworkid));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $data = array();
        $data['homework'] = $get->row();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('admin/master/homework/edit', $data, true)
        ));
    }

    public function process_edit_homework($params)
    {
        if (!isLogin() || !isTeacher()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk mengubah data');
        }

        $homeworkid = getPost('homeworkid');

        $cek = $this->mshomework->get(array('id' => $homeworkid));

        if ($cek->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $cek->row();

        $name = getPost('name');
        $collectiondate = getPost('collectiondate');
        $collectiontime = getPost('collectiontime');
        $document = $_FILES['document'];
        $description = getPost('description', null);

        if (empty(strlen(trim($name)))) {
            return JSONResponseDefault('FAILED', 'Nama tugas tidak boleh kosong');
        } else if ($collectiondate == null) {
            return JSONResponseDefault('FAILED', 'Tanggal akhir pengumpulan tidak boleh kosong');
        } else if ($collectiondate < getCurrentDate('Y-m-d')) {
            return JSONResponseDefault('FAILED', 'Tanggal akhir pengumpulan tidak boleh kurang dari tanggal sekarang');
        } else if ($collectiontime == null) {
            return JSONResponseDefault('FAILED', 'Jam akhir pengumpulan tidak boleh kosong');
        }

        // Validasi kombinasi tanggal dan waktu
        $collectionDateTime = $collectiondate . ' ' . $collectiontime;
        $currentDateTime = getCurrentDate('Y-m-d') . ' ' . getCurrentTime('H:i');
        if (strtotime($collectionDateTime) <= strtotime($currentDateTime)) {
            return JSONResponseDefault('FAILED', 'Waktu akhir pengumpulan harus lebih besar dari waktu sekarang');
        }

        $config = array();
        $config['allowed_types'] = 'jpg|jpeg|png|pdf|doc|docx';
        $config['upload_path'] = './uploads/homework';
        $config['encrypt_name'] = true;

        $this->load->library('upload', $config);

        $update = array();

        if ($document['size'] > 0) {
            if ($this->upload->do_upload('document')) {
                $data = $this->upload->data();
                $update['document'] = $data['file_name'];

                if ($row->document != null && file_exists('uploads/homework/' . $row->document)) {
                    @unlink('uploads/homework/' . $row->document);
                }
            } else {
                return JSONResponseDefault('FAILED', 'Gagal mengunggah dokumen');
            }
        }

        $update['name'] = $name;
        $update['collectiondate'] = $collectiondate;
        $update['collectiontime'] = $collectiontime;
        $update['description'] = $description;
        $update['updatedby'] = getCurrentIdSchool();
        $update['updateddate'] = getCurrentDate();

        $update = $this->mshomework->update(array('id' => $homeworkid), $update);

        if ($update) {
            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } else {
            return JSONResponseDefault('FAILED', 'Data gagal diubah');
        }
    }

    public function process_delete_homework($id)
    {
        if (!isLogin() || !isTeacher()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses untuk menghapus data');
        }

        $homeworkid = getPost('homeworkid');

        $cek = $this->mshomework->get(array('id' => $homeworkid));

        if ($cek->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $row = $cek->row();

        $delete = $this->mshomework->delete(array('id' => $homeworkid));

        if ($delete) {
            if ($row->document != null && file_exists('uploads/homework/' . $row->document)) {
                @unlink('uploads/homework/' . $row->document);
            }
            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Data gagal dihapus');
        }
    }

    public function evaluation($id)
    {
        if (!isLogin() || !isTeacher()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Tugas Peserta Didik';
        $data['content'] = 'admin/master/homework/evaluation/index';
        $data['homeworkdetail'] = $this->mshomeworkdetail->select('a.*,b.name as studentname')
            ->join('msstudent b', 'a.studentid = b.id')
            ->where('a.homeworkid', $id)
            ->get()
            ->result();

        return $this->load->view('master', $data);
    }

    public function evaluation_content($id)
    {

        $data = array();
        $data['homeworkdetail'] = $this->mshomeworkdetail->select('a.*,b.name')
            ->join('msstudent b', 'a.studentid = b.id')
            ->where('a.homeworkid', $id)
            ->get()
            ->result();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => $this->load->view('admin/master/homework/evaluation/content', $data, true)
        ));
    }

    public function process_evaluation($id)
    {
        try {

            $this->db->trans_begin();

            if (!isLogin() || !isTeacher()) {
                throw new Exception('Anda tidak memiliki akses untuk mengubah data');
            }

            $studentid = getPost('studentid', array());
            $score = getPost('score', array());
            $isdone = getPost('isdone', array());

            $homeworkid = $this->mshomework->get(array('id' => $id));

            if ($homeworkid->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            foreach ($score as $key => $value) {
                if ($value < 0) {
                    throw new Exception('Nilai tidak boleh kurang dari 0');
                } else if ($value > 100) {
                    throw new Exception('Nilai tidak boleh lebih dari 100');
                }
            }

            $homeworkdetail = $this->mshomeworkdetail->select('a.*')
                ->where('a.homeworkid', $id)
                ->where_in('a.studentid', $studentid)
                ->get()
                ->result();

            foreach ($homeworkdetail as $key => $value) {
                $update = array();
                $update['score'] = ($score[$value->studentid] ?? null) == null ? 0 : $score[$value->studentid];
                $update['status'] = ($isdone[$value->studentid] ?? null) == 1 ? 'Sudah Mengerjakan' : 'Belum Mengerjakan';
                $update['updatedby'] = getCurrentIdSchool();
                $update['updateddate'] = getCurrentDate();

                $this->mshomeworkdetail->update(array('homeworkid' => $id, 'studentid' => $value->studentid), $update);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal diubah');
            } else {
                $this->db->trans_commit();
                return JSONResponseDefault('OK', 'Data berhasil diubah');
            }
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }
}
