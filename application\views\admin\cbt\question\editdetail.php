<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="exampleModalLabel1">Ubah</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <form id="frmEditQuestion" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <div class="modal-body">
                <div class="row">
                    <div class="text-center">
                        <h4 class="mb-0">Ubah</h4>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <label for="question-edit" class="form-label">Soal <span class="text-danger">*</span></label>
                            <div id="question-edit"></div>
                        </div>
                        <div class="col-md-6 mt-2">
                            <div class="form-check form-check-inline mt-3">
                                <input type="radio" class="form-check-input" name="correctedit" id="correct-A-edit" value="1" <?= $checkedA ?>>
                                <label for="correct-A-edit" class="form-check-label">Jawaban (Pilih jika ini jawaban benar)</label>
                            </div>
                            <div id="answer-A-edit"></div>
                        </div>
                        <div class="col-md-6 mt-2">
                            <div class="form-check form-check-inline mt-3">
                                <input type="radio" class="form-check-input" name="correctedit" id="correct-B-edit" value="2" <?= $checkedB ?>>
                                <label for="correct-B-edit" class="form-check-label">Jawaban (Pilih jika ini jawaban benar)</label>
                            </div>
                            <div id="answer-B-edit"></div>
                        </div>
                        <div class="col-md-6 mt-2">
                            <div class="form-check form-check-inline mt-3">
                                <input type="radio" class="form-check-input" name="correctedit" id="correct-C-edit" value="3" <?= $checkedC ?>>
                                <label for="correct-C-edit" class="form-check-label">Jawaban (Pilih jika ini jawaban benar)</label>
                            </div>
                            <div id="answer-C-edit"></div>
                        </div>
                        <div class="col-md-6 mt-2">
                            <div class="form-check form-check-inline mt-3">
                                <input type="radio" class="form-check-input" name="correctedit" id="correct-D-edit" value="4" <?= $checkedD ?>>
                                <label for="correct-D-edit" class="form-check-label">Jawaban (Pilih jika ini jawaban benar)</label>
                            </div>
                            <div id="answer-D-edit"></div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary ms-2">
                    <i class="ti ti-check me-2"></i>
                    Simpan
                </button>
                <button type="button" class="btn btn-label-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </form>
    </div>
</div>
<script>
    $(document).ready(function() {
        loadQuil();

        $('#frmEditQuestion').submit(function(e) {
            e.preventDefault();
            $('button[type="submit"]').attr('disabled');
            let questionedit = cleanQuillContent($('#question-edit .ql-editor').html());
            let answerAedit = cleanQuillContent($('#answer-A-edit .ql-editor').html());
            let answerBedit = cleanQuillContent($('#answer-B-edit .ql-editor').html());
            let answerCedit = cleanQuillContent($('#answer-C-edit .ql-editor').html());
            let answerDedit = cleanQuillContent($('#answer-D-edit .ql-editor').html());
            let correctedit = $('input[name="correctedit"]:checked').val();

            // Validasi pertanyaan tidak boleh kosong atau hanya spasi
            if (!questionedit || questionedit.trim() === '' || questionedit === '<p><br></p>' || questionedit === '<p></p>') {
                swalMessageFailed('Pertanyaan tidak boleh kosong');
                $('button[type="submit"]').removeAttr('disabled');
                return;
            }

            let answeredit = [];

            if (answerAedit != '') {
                answeredit.push({
                    answer: answerAedit,
                    coulum: 1
                });
            }

            if (answerBedit != '') {
                answeredit.push({
                    answer: answerBedit,
                    coulum: 2
                });
            }

            if (answerCedit != '') {
                answeredit.push({
                    answer: answerCedit,
                    coulum: 3
                });
            }

            if (answerDedit != '') {
                answeredit.push({
                    answer: answerDedit,
                    coulum: 4
                });
            }

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                dataType: 'JSON',
                data: {
                    question: questionedit,
                    answer: answeredit,
                    correct: correctedit,
                    id: '<?= $id ?>'
                },
                success: function(response) {
                    if (response.RESULT == 'OK') {

                        return swalMessageSuccess(response.MESSAGE, (ok) => {
                            return window.location.reload();
                        });
                    } else {
                        $('#button[type="submit"]').removeAttr('disabled');
                        return swalMessageFailed(response.MESSAGE);
                    }
                },
                error: function() {
                    $('#button[type="submit"]').removeAttr('disabled');
                    return swalError();
                }
            });
        });
    });

    function loadQuil() {
        let questioneditoredit = new Quill("#question-edit", {
            bounds: "#question-edit",
            placeholder: "Type Something...",
            modules: {
                formula: !0,
                toolbar: [
                    [{
                        font: []
                    }, {
                        size: []
                    }],
                    ["bold", "italic", "underline", "strike"],
                    [{
                        color: []
                    }, {
                        background: []
                    }],
                    [{
                        script: "super"
                    }, {
                        script: "sub"
                    }],
                    [{
                        header: "1"
                    }, {
                        header: "2"
                    }, "blockquote", "code-block"],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }, {
                        indent: "-1"
                    }, {
                        indent: "+1"
                    }],
                    [{
                        direction: "rtl"
                    }],
                    ["link", "image", "video", "formula"],
                    ["clean"]
                ]
            },
            theme: "snow"
        });

        let answerAeditoredit = new Quill("#answer-A-edit", {
            bounds: "#answer-A-edit",
            placeholder: "Type Something...",
            modules: {
                formula: !0,
                toolbar: [
                    [{
                        font: []
                    }, {
                        size: []
                    }],
                    ["bold", "italic", "underline", "strike"],
                    [{
                        color: []
                    }, {
                        background: []
                    }],
                    [{
                        script: "super"
                    }, {
                        script: "sub"
                    }],
                    [{
                        header: "1"
                    }, {
                        header: "2"
                    }, "blockquote", "code-block"],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }, {
                        indent: "-1"
                    }, {
                        indent: "+1"
                    }],
                    [{
                        direction: "rtl"
                    }],
                    ["link", "image", "video", "formula"],
                    ["clean"]
                ]
            },
            theme: "snow"
        });

        let answerBeditoredit = new Quill("#answer-B-edit", {
            bounds: "#answer-B-edit",
            placeholder: "Type Something...",
            modules: {
                formula: !0,
                toolbar: [
                    [{
                        font: []
                    }, {
                        size: []
                    }],
                    ["bold", "italic", "underline", "strike"],
                    [{
                        color: []
                    }, {
                        background: []
                    }],
                    [{
                        script: "super"
                    }, {
                        script: "sub"
                    }],
                    [{
                        header: "1"
                    }, {
                        header: "2"
                    }, "blockquote", "code-block"],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }, {
                        indent: "-1"
                    }, {
                        indent: "+1"
                    }],
                    [{
                        direction: "rtl"
                    }],
                    ["link", "image", "video", "formula"],
                    ["clean"]
                ]
            },
            theme: "snow"
        });

        let answerCeditoredit = new Quill("#answer-C-edit", {
            bounds: "#answer-C-edit",
            placeholder: "Type Something...",
            modules: {
                formula: !0,
                toolbar: [
                    [{
                        font: []
                    }, {
                        size: []
                    }],
                    ["bold", "italic", "underline", "strike"],
                    [{
                        color: []
                    }, {
                        background: []
                    }],
                    [{
                        script: "super"
                    }, {
                        script: "sub"
                    }],
                    [{
                        header: "1"
                    }, {
                        header: "2"
                    }, "blockquote", "code-block"],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }, {
                        indent: "-1"
                    }, {
                        indent: "+1"
                    }],
                    [{
                        direction: "rtl"
                    }],
                    ["link", "image", "video", "formula"],
                    ["clean"]
                ]
            },
            theme: "snow"
        });

        let answerDeditoredit = new Quill("#answer-D-edit", {
            bounds: "#answer-D-edit",
            placeholder: "Type Something...",
            modules: {
                formula: !0,
                toolbar: [
                    [{
                        font: []
                    }, {
                        size: []
                    }],
                    ["bold", "italic", "underline", "strike"],
                    [{
                        color: []
                    }, {
                        background: []
                    }],
                    [{
                        script: "super"
                    }, {
                        script: "sub"
                    }],
                    [{
                        header: "1"
                    }, {
                        header: "2"
                    }, "blockquote", "code-block"],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }, {
                        indent: "-1"
                    }, {
                        indent: "+1"
                    }],
                    [{
                        direction: "rtl"
                    }],
                    ["link", "image", "video", "formula"],
                    ["clean"]
                ]
            },
            theme: "snow"
        });

        $('#question-edit .ql-editor').html('<?= $question ?>');
        $('#answer-A-edit .ql-editor').html('<?= $answerA ?>');
        $('#answer-B-edit .ql-editor').html('<?= $answerB ?>');
        $('#answer-C-edit .ql-editor').html('<?= $answerC ?>');
        $('#answer-D-edit .ql-editor ').html('<?= $answerD ?> ');

        questioneditoredit.getModule('toolbar').addHandler('image', function() {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.onchange = function() {
                var file = input.files[0];
                var reader = new FileReader();
                reader.onload = function() {
                    var img = new Image();
                    img.src = reader.result;
                    img.onload = function() {
                        // Periksa ukuran gambar
                        if (img.width > 500 || img.height > 500) {
                            return swalMessageFailed('Ukuran gambar terlalu besar. Harap unggah gambar dengan ukuran maksimum 500x500 piksel.');
                        } else {
                            var range = questioneditoredit.getSelection(true);
                            questioneditoredit.insertEmbed(range.index, 'image', img.src);
                        }
                    };
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });

        answerAeditoredit.getModule('toolbar').addHandler('image', function() {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.onchange = function() {
                var file = input.files[0];
                var reader = new FileReader();
                reader.onload = function() {
                    var img = new Image();
                    img.src = reader.result;
                    img.onload = function() {
                        // Periksa ukuran gambar
                        if (img.width > 500 || img.height > 500) {
                            return swalMessageFailed('Ukuran gambar terlalu besar. Harap unggah gambar dengan ukuran maksimum 500x500 piksel.');
                        } else {
                            var range = answerAeditoredit.getSelection(true);
                            answerAeditoredit.insertEmbed(range.index, 'image', img.src);
                        }
                    };
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });

        answerBeditoredit.getModule('toolbar').addHandler('image', function() {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.onchange = function() {
                var file = input.files[0];
                var reader = new FileReader();
                reader.onload = function() {
                    var img = new Image();
                    img.src = reader.result;
                    img.onload = function() {
                        // Periksa ukuran gambar
                        if (img.width > 500 || img.height > 500) {
                            return swalMessageFailed('Ukuran gambar terlalu besar. Harap unggah gambar dengan ukuran maksimum 500x500 piksel.');
                        } else {
                            var range = answerBeditoredit.getSelection(true);
                            answerBeditoredit.insertEmbed(range.index, 'image', img.src);
                        }
                    };
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });

        answerCeditoredit.getModule('toolbar').addHandler('image', function() {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.onchange = function() {
                var file = input.files[0];
                var reader = new FileReader();
                reader.onload = function() {
                    var img = new Image();
                    img.src = reader.result;
                    img.onload = function() {
                        // Periksa ukuran gambar
                        if (img.width > 500 || img.height > 500) {
                            return swalMessageFailed('Ukuran gambar terlalu besar. Harap unggah gambar dengan ukuran maksimum 500x500 piksel.');
                        } else {
                            var range = answerCeditoredit.getSelection(true);
                            answerCeditoredit.insertEmbed(range.index, 'image', img.src);
                        }
                    };
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });

        answerDeditoredit.getModule('toolbar').addHandler('image', function() {
            var input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.onchange = function() {
                var file = input.files[0];
                var reader = new FileReader();
                reader.onload = function() {
                    var img = new Image();
                    img.src = reader.result;
                    img.onload = function() {
                        // Periksa ukuran gambar
                        if (img.width > 500 || img.height > 500) {
                            return swalMessageFailed('Ukuran gambar terlalu besar. Harap unggah gambar dengan ukuran maksimum 500x500 piksel.');
                        } else {
                            var range = answerDeditoredit.getSelection(true);
                            answerDeditoredit.insertEmbed(range.index, 'image', img.src);
                        }
                    };
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });

    }
</script>