<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<h4 class="py-3 mb-4">
    <span class="text-muted fw-light">Berita Sekolah / Berita / </span> Ubah Berita
</h4>

<div class="row">
    <div class="col-md-12">
        <form id="frmAddnews" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
            <div class="card mb-3">
                <h5 class="card-header">Formulir Ubah Berita</h5>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="title" class="form-label">Judul Berita<span class="text-danger">*</span></label>
                                <input type="text" placeholder="Masukkan judul Berita" id="title" name="title" class="form-control" value="<?= htmlspecialchars($news->title) ?>" required>
                            </div>
                        </div>

                        <!-- Kategori dan Thumbnail -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="kategori_id" class="form-label">Kategori Berita<span class="text-danger">*</span></label>
                                <select name="kategori_id" id="kategori_id" class="select2 form-select">
                                    <option value="" disabled selected>Pilih salah satu</option>
                                    <?php foreach ($kategori as $key => $value) : ?>
                                        <option value="<?= $value->id; ?>" <?= ($value->id == $news->categoryid) ? 'selected' : '' ?>><?= $value->name ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="thumbnail" class="form-label">Thumbnail Berita<span>* Maks 3 MB</span></label>
                                <input class="form-control" type="file" id="thumbnail" name="thumbnail" accept="image/*" onchange="previewImage(event)">
                                <small class="form-text text-muted">Kosongkan jika tidak ingin diubah</small>
                                <div class="thumbnail-container" style="width: 100px; height: 100px; overflow: hidden; display: inline-block; margin-top: 10px;">
                                    <img id="thumbnailPreview" src="<?= base_url('uploads/news/' . $news->thumbnail) ?>" alt="Thumbnail" class="img-thumbnail" style="width: 100%; height: 100%; object-fit: cover;">
                                </div>
                            </div>
                        </div>

                        <!-- Isi Berita -->
                        <div class="col-md-12 mt-2">
                            <div class="mb-3">
                                <label for="isi" class="form-label">Isi Berita<span class="text-danger">*</span></label>
                                <div id="isi" class="ql-editor">
                                    <?= $news->content ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-flex justify-content-end mt-4">
                <a href="<?= base_url('master/news') ?>" class="btn btn-danger">
                    <i class="ti ti-arrow-back me-2"></i>
                    Kembali
                </a>

                <button type="submit" class="btn btn-primary ms-2">
                    <i class="ti ti-check me-2"></i>
                    Simpan
                </button>
            </div>
        </form>
    </div>
</div>

<!-- <script src="<?= base_url('assets') ?>/plugins/ckeditor/ckeditor.js"></script>

<script>
    CKEDITOR.config.versionCheck = false;
    CKEDITOR.replace('isi');
</script> -->

<script>
    window.onload = function() {
        let isiBeritaEditor = new Quill("#isi", {
            bounds: "#isi",
            placeholder: "Ketik isi berita di sini...",
            modules: {
                formula: true,
                toolbar: [
                    [{
                        font: []
                    }, {
                        size: []
                    }],
                    ["bold", "italic", "underline", "strike"],
                    [{
                        color: []
                    }, {
                        background: []
                    }],
                    [{
                        script: "super"
                    }, {
                        script: "sub"
                    }],
                    [{
                        header: "1"
                    }, {
                        header: "2"
                    }, "blockquote", "code-block"],
                    [{
                        list: "ordered"
                    }, {
                        list: "bullet"
                    }, {
                        indent: "-1"
                    }, {
                        indent: "+1"
                    }],
                    [{
                        direction: "rtl"
                    }],
                    ["link", "image", "video", "formula"],
                    ["clean"]
                ]
            },
            theme: "snow"
        });

        const maxWidth = 600;
        const maxHeight = 600;


        let toolbar = isiBeritaEditor.getModule('toolbar');
        toolbar.addHandler('image', function() {
            const input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');

            input.click();

            input.onchange = function() {
                const file = input.files[0];

                if (file) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        const img = new Image();

                        img.onload = function() {
                            // Memeriksa dimensi gambar
                            if (img.width > maxWidth || img.height > maxHeight) {
                                swalMessageFailed(`Gambar terlalu besar. Maksimal dimensi adalah ${maxWidth}x${maxHeight} px.`);
                                return;
                            }

                            const range = isiBeritaEditor.getSelection();
                            isiBeritaEditor.insertEmbed(range.index, 'image', e.target.result);
                        };

                        img.src = e.target.result;
                    };

                    reader.readAsDataURL(file);
                }
            };
        });

        $('#frmAddnews').submit(function(e) {
            e.preventDefault();
            $('button[type="submit"]').attr("disabled", true);

            let isiBerita = cleanQuillContent($('#isi .ql-editor').html());
            console.log("Isi Berita yang dikirim: ", isiBerita);

            if (!isiBerita || isiBerita === "<p><br></p>" || isiBerita === "") {
                swalMessageFailed("Isi Berita tidak boleh kosong");
                $('button[type="submit"]').removeAttr("disabled");
                return;
            }

            let formData = new FormData(this);
            formData.append("isi", isiBerita);

            $.ajax({
                url: $(this).attr("action"),
                type: "POST",
                dataType: "JSON",
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.RESULT === "OK") {
                        return swalMessageSuccess(response.MESSAGE, (ok) => {
                            $('button[type="submit"]').removeAttr("disabled");
                            return window.location.href = '<?= base_url('master/news') ?>';
                        });
                    } else {
                        swalMessageFailed(response.MESSAGE);
                        $('button[type="submit"]').removeAttr("disabled");
                    }
                },
                error: function() {
                    $('button[type="submit"]').removeAttr("disabled");
                    swalError();
                }
            });
        });
    }

    function cleanQuillContent(content) {
        content = content.trim();

        const strippedContent = content.replace(/(<([^>]+)>)/gi, "").trim();

        return strippedContent.length === 0 ? "" : content;
    }

    function previewImage(event) {
        const file = event.target.files[0];
        const preview = document.getElementById('thumbnailPreview');

        if (file) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            }

            reader.readAsDataURL(file);
        } else {
            preview.style.display = 'none';
        }
    };
</script>